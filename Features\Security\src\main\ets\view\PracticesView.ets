// 导入通用模块中的全局信息模型和页面上下文类型
import type { GlobalInfoModel, PageContext } from '@ohos/common';

// 导入通用业务模块中的相关组件和类型
import {
  BaseHomeView,
  FullScreenNavigation,
  LoadingMoreItemBuilder,
} from '@ohos/commonbusiness';

// 导入实践状态类型
import type { PracticeState } from '../viewmodel/PracticeState';
// 导入实践视图模型
import { PracticeViewModel } from '../viewmodel/PracticeViewModel';

// 使用Component装饰器定义实践视图组件，设置非活跃时冻结
@Component({ freezeWhenInactive: true })
export struct PracticesView {
  // 定义视图模型实例
  viewModel: PracticeViewModel = PracticeViewModel.getInstance();
  // 使用StorageProp装饰器获取全局信息模型
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel =
    AppStorage.get('GlobalInfoModel')!;
  // 使用State装饰器定义实践状态
  @State practiceState: PracticeState = this.viewModel.getState();
  // 定义列表滚动器私有变量
  private listScroller: Scroller = new Scroller();
  // 定义示例页面上下文私有变量
  private samplePageContext: PageContext = AppStorage.get('samplePageContext')!;

  // 定义即将出现生命周期方法
  aboutToAppear(): void {
    // 组件初始化
  }


  // 使用Builder装饰器定义内容视图构建器
  @Builder
  ContentViewBuilder() {
    // 创建列表组件
    List({ scroller: this.listScroller }) {
      // 创建列表项
      ListItem() {
        // 创建加载更多项目构建器
        LoadingMoreItemBuilder(this.practiceState.loadingModel)
      }
    }
    // 设置宽度
    .width('100%')
    // 设置高度
    .height('100%')
    // 设置背景颜色
    .backgroundColor($r('sys.color.background_secondary'))
  }
              }
            }, (item: SampleCategory) => `${item.categoryType}_${item.sampleCards?.length}`)
          }
          // 禁用标签页滚动
          .scrollable(false)
          // 设置标签栏高度为0
          .barHeight(0)
        }
      }
      // 设置列表项组内边距
      .padding({
        left: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.LG ? CommonConstants.TAB_BAR_WIDTH : 0,
      })
    }
    // 隐藏列表滚动条
    .scrollBar(BarState.Off)
    // 设置列表宽度为100%
    .width('100%')
    // 设置列表高度为100%
    .height('100%')
    // 禁用列表裁剪
    .clip(false)
    // 根据状态设置边缘效果
    .edgeEffect(this.practiceState.hasEdgeEffect ? EdgeEffect.Spring : EdgeEffect.None)
    // 设置粘性样式为头部
    .sticky(StickyStyle.Header)
    // 设置滚动帧开始事件
    .onScrollFrameBegin((offset: number, state: ScrollState) => {
      // 创建计算高度参数
      const param: CalculateHeightParam = { offset, state, yOffset: this.listScroller.currentOffset().yOffset };
      // 发送计算横幅高度事件
      const bannerChangeHeight: boolean | void = this.viewModel.sendEvent<CalculateHeightParam>({
        type: BaseHomeEventType.CALCULATE_BANNER_HEIGHT,
        param,
      });
      // 如果横幅高度发生变化
      if (bannerChangeHeight) {
        // 返回剩余偏移量为0
        return { offsetRemain: 0 };
      }
      // 返回原始偏移量
      return { offsetRemain: offset };
    })
    // 设置滚动完成事件
    .onDidScroll(() => {
      // 发送处理滚动偏移事件
      this.viewModel.sendEvent<OffsetParam>({
        type: BaseHomeEventType.HANDLE_SCROLL_OFFSET,
        param: { yOffset: this.listScroller.currentOffset().yOffset, tabIndex: TabBarType.SECURITY },
      });
    })
    // 设置列表背景颜色
    .backgroundColor($r('sys.color.background_secondary'))
  }

  // 使用Builder装饰器定义顶部标题视图构建器
  @Builder
  TopTitleViewBuilder() {
    // 创建全屏导航组件
    FullScreenNavigation({
      topNavigationData: this.practiceState.topNavigationData,
      tabView: () => {
        // 调用分类头部构建器
        this.CategoryHeaderBuilder()
      },
    })
  }

  // 定义构建方法
  build() {
    // 创建导航组件
    Navigation(this.samplePageContext.navPathStack) {
      // 创建基础主页视图
      BaseHomeView({
        loadingModel: this.practiceState.loadingModel,
        contentView: () => {
          // 调用内容视图构建器
          this.ContentViewBuilder()
        },
        topTitleView: () => {
          // 调用顶部标题视图构建器
          this.TopTitleViewBuilder()
        },
        reloadData: () => {
          // 发送加载示例页面事件
          this.viewModel.sendEvent({ type: PracticeEventType.LOAD_SAMPLE_PAGE, param: null });
        },
      })
    }
    // 设置导航模式为堆栈
    .mode(NavigationMode.Stack)
    // 隐藏标题栏
    .hideTitleBar(true)
  }
}