// 导入能力工具包中的通用类型
import type { common } from '@kit.AbilityKit';
// 导入能力工具包中的配置常量
import { ConfigurationConstant } from '@kit.AbilityKit';
// 导入ArkUI工具包中的提示操作
import { promptAction } from '@kit.ArkUI';
// 导入通用模块中的全局信息模型类型
import type { GlobalInfoModel } from '@ohos/common';
// 导入通用模块中的断点类型枚举、通用常量、进程工具、窗口工具
import { BreakpointTypeEnum, CommonConstants, ProcessUtil, WindowUtil } from '@ohos/common';
// 导入通用业务模块中的标签内容状态和标签栏类型
import { TAB_CONTENT_STATUSES, TabBarType } from '@ohos/commonbusiness';
// 导入首页模块中的组件列表视图
import { ComponentListView } from '@ohos/home';
// 导入安全模块中的实践视图
import { PracticesView } from '@ohos/security';
// 导入社区模块中的探索视图
import { ExplorationView } from '@ohos/community';
// 导入我的模块中的我的视图
import { MineView } from '@ohos/mine';
// 导入自定义侧边栏组件
import { CustomSideBar } from '../component/CustomSideBar';
// 导入自定义标签栏组件
import { CustomTabBar } from '../component/CustomTabBar';

// 按压时间常量，用于双击退出功能
const PRESS_TIME = 1500;

/**
 * 主页面构建器函数
 * 用于创建主页面组件实例
 */
@Builder
export function MainPageBuilder() {
  MainPage()
}

/**
 * 主页面组件
 * 应用的主要页面，包含标签栏导航和各个功能模块
 * 支持响应式设计，在不同屏幕尺寸下显示不同的布局
 * 启用非活跃时冻结功能以优化性能
 */
@Component({ freezeWhenInactive: true })
struct MainPage {
  // 全局信息模型，包含断点信息和设备信息
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  // 系统颜色模式，监听变化并触发处理函数
  @StorageProp('systemColorMode') @Watch('handleColorModeChange') systemColorMode: ConfigurationConstant.ColorMode =
    AppStorage.get('systemColorMode')!;
  // 当前选中的标签索引
  @State currentIndex: number = 0;
  // 标签控制器，用于控制标签页切换
  private tabController: TabsController = new TabsController();
  // 返回按键按压时间，用于双击退出功能
  private backPressTime: number = 0;
  // 页面是否已显示的标志
  private isShown: boolean = false;

  /**
   * 标签组件构建器
   * 创建包含所有功能模块的标签页容器
   */
  @Builder
  TabComponent() {
    // 创建标签页容器，使用控制器和当前索引
    Tabs({ controller: this.tabController, index: this.currentIndex }) {
      // 首页标签内容
      TabContent() {
        // 组件列表视图
        ComponentListView()
      }
      // 设置标签内容高度为100%
      .height('100%')

      // 安全标签内容（对应示例模块）
      TabContent() {
        // 实践视图
        PracticesView()
      }
      // 设置标签内容高度为100%
      .height('100%')

      // 社区标签内容（对应实践模块）
      TabContent() {
        // 探索视图
        ExplorationView()
      }
      // 设置标签内容高度为100%
      .height('100%')

      // 我的标签内容
      TabContent() {
        // 我的视图
        MineView()
      }
      // 设置标签内容高度为100%
      .height('100%')
    }
    // 标签页附加时的回调，预加载指定标签项
    .onAttach(() => {
      this.tabController.preloadItems([TabBarType.HOME, TabBarType.SECURITY, TabBarType.COMMUNITY])
    })
    // 动画开始时的回调，更新标签状态和索引
    .onAnimationStart((index: number, targetIndex: number) => {
      this.changeTabStatus(targetIndex);
      this.currentIndex = targetIndex;
    })
    // 设置动画模式为无动画
    .animationMode(AnimationMode.NO_ANIMATION)
    // 隐藏标签栏宽度
    .barWidth(0)
    // 隐藏标签栏高度
    .barHeight(0)
    // 禁用滚动
    .scrollable(false)
    // 设置背景颜色
    .backgroundColor($r('sys.color.background_secondary'))
    // 设置高度为100%
    .height('100%')
    // 设置宽度为100%
    .width('100%')
  }

  /**
   * 组件即将出现时的生命周期回调
   * 初始化当前标签索引
   */
  aboutToAppear(): void {
    // 设置当前标签索引为0（首页）
    AppStorage.setOrCreate('currentTabIndex', 0);
  }

  /**
   * 返回按键处理方法
   * 实现双击退出功能
   * @returns 是否拦截返回事件
   */
  onBackPress(): boolean {
    // 获取当前时间
    const newTime = new Date().getTime();
    // 如果在指定时间内连续按压返回键，则退到后台
    if (this.backPressTime && newTime - this.backPressTime < PRESS_TIME) {
      ProcessUtil.moveAbilityToBackground(getContext(this) as common.UIAbilityContext);
      return false;
    }
    // 记录按压时间
    this.backPressTime = newTime;
    // 显示提示信息
    promptAction.showToast({ message: $r('app.string.back_toast'), duration: PRESS_TIME });
    return true;
  }

  /**
   * 切换标签状态方法
   * 更新标签索引并调整状态栏颜色
   * @param index 目标标签索引
   */
  changeTabStatus(index: number) {
    // 更新应用存储中的当前标签索引
    AppStorage.setOrCreate('currentTabIndex', index);
    const selectedIndex = index;
    // 判断当前是否为深色模式
    const isSystemDark: boolean = (this.systemColorMode === ConfigurationConstant.ColorMode.COLOR_MODE_DARK);
    // 如果是我的标签或大屏设备，使用系统深色模式设置状态栏
    if (selectedIndex === TabBarType.MINE || this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.LG ||
      this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL) {
      WindowUtil.updateStatusBarColor(getContext(this), isSystemDark);
    } else {
      // 其他情况根据标签内容状态设置状态栏颜色
      WindowUtil.updateStatusBarColor(getContext(this), isSystemDark || TAB_CONTENT_STATUSES[selectedIndex]);
    }
  }

  /**
   * 处理颜色模式变化
   * 当系统颜色模式改变时更新标签状态
   */
  handleColorModeChange() {
    // 如果页面已显示，则更新当前标签状态
    if (this.isShown) {
      this.changeTabStatus(this.currentIndex);
    }
  }

  /**
   * 构建主页面组件的UI结构
   * 使用导航目标容器和侧边栏容器实现响应式布局
   */
  build() {
    // 创建导航目标容器
    NavDestination() {
      // 创建嵌入式侧边栏容器
      SideBarContainer(SideBarContainerType.Embed) {
        // 自定义侧边栏组件
        CustomSideBar({
          // 传递当前选中索引
          currentIndex: this.currentIndex,
          // 侧边栏切换回调
          sideBarChange: (currentIndex: number) => {
            // 更新标签状态
            this.changeTabStatus(currentIndex);
            // 更新当前索引
            this.currentIndex = currentIndex;
          }
        });
        // 创建底部对齐的堆叠容器
        Stack({ alignContent: Alignment.BottomStart }) {
          // 标签组件
          this.TabComponent()
          // 自定义标签栏组件
          CustomTabBar({
            // 传递当前选中索引
            currentIndex: this.currentIndex,
            // 标签栏切换回调
            tabBarChange: (currentIndex: number) => {
              // 更新标签状态
              this.changeTabStatus(currentIndex);
              // 更新当前索引
              this.currentIndex = currentIndex;
            }
          })
            // 在超大屏设备上隐藏标签栏
            .visibility(this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ? Visibility.None :
            Visibility.Visible)
        }
      }
      // 在超大屏设备上显示侧边栏
      .showSideBar(this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL)
      // 隐藏控制按钮
      .showControlButton(false)
    }
    // 设置透明度转场效果
    .transition(TransitionEffect.OPACITY)
    // 隐藏标题栏
    .hideTitleBar(true)
    // 设置背景颜色
    .backgroundColor($r('sys.color.background_secondary'))
    // 返回按键处理
    .onBackPressed(() => {
      this.onBackPress();
      return true;
    })
    // 页面显示时的回调
    .onShown(() => {
      // 设置页面已显示标志
      this.isShown = true;
      // 处理颜色模式变化
      this.handleColorModeChange();
      // 延迟500毫秒后关闭模糊渲染组
      CommonConstants.PROMISE_WAIT(500).then(() => {
        AppStorage.setOrCreate('BlurRenderGroup', false);
      })
    })
    // 页面隐藏时的回调
    .onHidden(() => {
      // 开启模糊渲染组
      AppStorage.setOrCreate('BlurRenderGroup', true);
      // 设置页面未显示标志
      this.isShown = false;
    })
    // 设置高度为100%
    .height('100%')
    // 设置宽度为100%
    .width('100%')
  }
}