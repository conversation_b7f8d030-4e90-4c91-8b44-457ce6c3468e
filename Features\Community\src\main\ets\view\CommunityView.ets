
// 导入通用模块中的全局信息模型和页面上下文类型
import type { GlobalInfoModel, PageContext } from '@ohos/common';

// 导入通用业务模块中的相关组件和类型
import {
  BaseHomeView,
  FullScreenNavigation,
  LoadingMoreItemBuilder,
} from '@ohos/commonbusiness';

// 导入探索状态类型
import type { CommunityState } from '../viewmodel/CommunityState';
// 导入探索视图模型
import { CommunityViewModel } from '../viewmodel/CommunityViewModel';

// 使用Component装饰器定义探索视图组件，启用非活动时冻结优化
@Component({ freezeWhenInactive: true })
export struct CommunityView {
  // 定义视图模型
  viewModel: CommunityViewModel = CommunityViewModel.getInstance();
  // 使用StorageProp装饰器定义全局信息模型
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel =
    AppStorage.get('GlobalInfoModel')!;
  // 使用State装饰器定义探索状态
  @State explorationState: CommunityState = this.viewModel.getState();
  // 定义私有列表滚动器
  private listScroller: Scroller = new Scroller();
  // 定义私有探索页面上下文
  private explorationPageContext: PageContext = AppStorage.get('explorationPageContext')!;

  // 定义即将出现的生命周期方法
  aboutToAppear(): void {
    // 组件初始化
  }

  // 使用Builder装饰器定义内容视图构建器
  @Builder
  ContentViewBuilder() {
    // 创建列表组件
    List({ scroller: this.listScroller }) {
      // 创建列表项
      ListItem() {
        // 创建加载更多项目构建器
        LoadingMoreItemBuilder(this.explorationState.loadingModel)
      }
    }
    // 设置宽度
    .width('100%')
    // 设置高度
    .height('100%')
    // 设置背景颜色
    .backgroundColor($r('sys.color.background_secondary'))
  }

  // 使用Builder装饰器定义顶部标题视图构建器
  @Builder
  TopTitleViewBuilder() {
    // 创建全屏导航
    FullScreenNavigation({
      topNavigationData: this.explorationState.topNavigationData,
    })
  }

  // 定义构建方法
  build() {
    // 创建导航组件
    Navigation(this.explorationPageContext.navPathStack) {
      // 创建基础首页视图
      BaseHomeView({
        loadingModel: this.explorationState.loadingModel,
        contentView: () => {
          // 调用内容视图构建器
          this.ContentViewBuilder()
        },
        topTitleView: () => {
          // 调用顶部标题视图构建器
          this.TopTitleViewBuilder()
        },
        reloadData: () => {
          // 重新加载数据
        },
      })
    }
    // 设置导航模式
    .mode(NavigationMode.Stack)
    // 隐藏标题栏
    .hideTitleBar(true)
  }
}