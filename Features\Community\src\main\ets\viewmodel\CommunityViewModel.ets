// 导入通用业务模块中的相关类型和组件
import {
  BaseHomeEventParam,
  BaseHomeEventType,
  BaseHomeViewModel,
} from '@ohos/commonbusiness';

// 导入探索状态
import { CommunityState } from './CommunityState';

// 导出探索视图模型类，继承基础首页视图模型
export class CommunityViewModel extends BaseHomeViewModel<CommunityState> {
  // 定义私有静态实例
  private static instance: CommunityViewModel;

  // 定义私有构造函数
  private constructor() {
    // 调用父类构造函数
    super(new CommunityState());
    // 设置顶部导航数据标题
    this.state.topNavigationData.title = $r('app.string.community_name');
  }

  // 定义获取实例的公共静态方法
  public static getInstance(): CommunityViewModel {
    // 如果实例不存在
    if (!CommunityViewModel.instance) {
      // 创建新实例
      CommunityViewModel.instance = new CommunityViewModel();
    }
    // 返回实例
    return CommunityViewModel.instance;
  }

  // 定义发送事件的方法
  sendEvent<T>(eventParam: BaseHomeEventParam<T>): void | boolean {
    // 调用父类发送事件方法
    return super.sendEvent(eventParam);
  }
}

// 导出探索事件参数接口
export interface CommunityEventParam<T> {
  // 事件类型
  type: BaseHomeEventType;
  // 参数
  param: T;
}