// 导入能力工具包中的配置常量
import { ConfigurationConstant } from '@kit.AbilityKit';
// 导入通用模块中的全局信息模型和页面上下文类型
import type { GlobalInfoModel, PageContext } from '@ohos/common';
// 导入通用模块中的断点类型、断点类型枚举和通用常量
import { BreakpointType, BreakpointTypeEnum, CommonConstants } from '@ohos/common';
// 导入通用业务模块中的横幅数据类型
import type { BannerData } from '@ohos/commonbusiness';
// 导入通用业务模块中的相关组件和类型
import {
  BannerCard,
  BaseHomeEventType,
  BaseHomeView,
  CalculateHeightParam,
  FullScreenNavigation,
  LoadingMoreItemBuilder,
  OffsetParam,
  TabBarType,
} from '@ohos/commonbusiness';
// 导入开发者卡片组件
import { DeveloperCard } from '../component/DeveloperCard';
// 导入体验卡片组件
import { ExperienceCard } from '../component/ExperienceCard';
// 导入动态卡片组件
import { FeedCard } from '../component/FeedCard';
// 导入发现卡片数据和发现内容类型
import type { DiscoverCardData, DiscoverContent } from '../model/DiscoverData';
// 导入文章类型枚举
import { ArticleTypeEnum } from '../model/DiscoverData';
// 导入探索状态类型
import type { ExplorationState } from '../viewmodel/ExplorationState';
// 导入探索事件类型和探索视图模型
import { ExplorationEventType, ExplorationViewModel } from '../viewmodel/ExplorationViewModel';

// 使用Component装饰器定义探索视图组件，启用非活动时冻结优化
@Component({ freezeWhenInactive: true })
export struct ExplorationView {
  // 定义视图模型
  viewModel: ExplorationViewModel = ExplorationViewModel.getInstance();
  // 使用StorageProp和Watch装饰器定义全局信息模型
  @StorageProp('GlobalInfoModel') @Watch('handleBreakPointChange') globalInfoModel: GlobalInfoModel =
    AppStorage.get('GlobalInfoModel')!;
  // 使用StorageProp和Watch装饰器定义系统颜色模式
  @StorageProp('systemColorMode') @Watch('handleColorModeChange') systemColorMode: ConfigurationConstant.ColorMode =
    AppStorage.get('systemColorMode')!;
  // 使用State装饰器定义探索状态
  @State explorationState: ExplorationState = this.viewModel.getState();
  // 定义私有列表滚动器
  private listScroller: Scroller = new Scroller();
  // 定义私有探索页面上下文
  private explorationPageContext: PageContext = AppStorage.get('explorationPageContext')!;

  // 定义即将出现的生命周期方法
  aboutToAppear(): void {
    // 发送加载发现页面事件
    this.viewModel.sendEvent({ type: ExplorationEventType.LOAD_DISCOVERY_PAGE, param: null });
  }

  // 定义处理断点变化的方法
  handleBreakPointChange() {
    // 发送处理断点变化事件
    this.viewModel.sendEvent<OffsetParam>({
      type: BaseHomeEventType.HANDLE_BREAKPOINT_CHANGE,
      param: { yOffset: (this.listScroller?.currentOffset()?.yOffset || 0), tabIndex: TabBarType.COMMUNITY },
    });
  }

  // 定义处理颜色模式变化的方法
  handleColorModeChange() {
    // 发送处理颜色变化事件
    this.viewModel.sendEvent<OffsetParam>({
      type: BaseHomeEventType.HANDLE_COLOR_CHANGE,
      param: { yOffset: (this.listScroller?.currentOffset()?.yOffset || 0), tabIndex: TabBarType.COMMUNITY },
    });
  }

  // 定义跳转文章详情的方法
  jumpArticleDetail(componentContent: DiscoverContent) {
    // 发送跳转详情事件
    this.viewModel.sendEvent<DiscoverContent>({
      type: ExplorationEventType.JUMP_DETAIL_DETAIL,
      param: componentContent,
    });
  }

  // 定义跳转横幅详情的方法
  jumpBannerDetail(banner: BannerData) {
    // 发送跳转横幅详情事件
    this.viewModel.sendEvent<BannerData>({ type: BaseHomeEventType.JUMP_BANNER_DETAIL, param: banner });
  }

  // 使用Builder装饰器定义分类头部构建器
  @Builder
  CategoryHeaderBuilder(groupItem: string) {
    // 创建行布局
    Row() {
      // 创建文本组件
      Text(groupItem)
        // 设置字体大小
        .fontSize($r('sys.float.Subtitle_L'))
        // 设置字体粗细
        .fontWeight(FontWeight.Bold)
        // 设置字体颜色
        .fontColor($r('sys.color.font_primary'))
    }
    // 设置内容对齐方式
    .justifyContent(FlexAlign.Start)
    // 设置宽度
    .width('100%')
    // 设置内边距
    .padding({
      top: $r('sys.float.padding_level4'),
      bottom: $r('sys.float.padding_level4'),
      left: new BreakpointType<Length>({
        sm: $r('sys.float.padding_level8'),
        md: $r('sys.float.padding_level12'),
        lg: CommonConstants.SPACE_32 + CommonConstants.TAB_BAR_WIDTH,
        xl: $r('sys.float.padding_level16'),
      }).getValue(this.globalInfoModel.currentBreakpoint),
    })
  }

  // 使用Builder装饰器定义内容视图构建器
  @Builder
  ContentViewBuilder() {
    // 创建列表组件
    List({ scroller: this.listScroller, space: CommonConstants.SPACE_16 }) {
      // 创建列表项
      ListItem() {
        // 创建横幅卡片
        BannerCard({
          tabViewType: TabBarType.COMMUNITY,
          bannerState: this.explorationState.bannerState,
          handleItemClick: (banner: BannerData) => {
            // 发送跳转横幅详情事件
            this.viewModel.sendEvent<BannerData>({ type: BaseHomeEventType.JUMP_BANNER_DETAIL, param: banner });
          },
        })
      }
      // 设置高度
      .height(this.explorationState.bannerHeight)

      // 创建重复组件
      Repeat(this.explorationState.discoveryData)
        // 设置每个项目的渲染
        .each((repeatItem: RepeatItem<DiscoverCardData>) => {
          // 创建列表项
          ListItem() {
            // 创建列布局
            Column() {
              // 调用分类头部构建器
              this.CategoryHeaderBuilder(repeatItem.item.name)
              // 创建动态卡片
              FeedCard({
                discoverContents: repeatItem.item.contents,
                handleItemClick: (content: DiscoverContent) => {
                  // 跳转文章详情
                  this.jumpArticleDetail(content);
                },
              })
            }
          }
        })
        // 设置键值
        .key((item: DiscoverCardData) => item.id.toString())
        // 设置模板ID
        .templateId((item: DiscoverCardData) => item.type.toString())
        // 设置体验模板
        .template(ArticleTypeEnum.EXPERIENCES.toString(), (repeatItem: RepeatItem<DiscoverCardData>) => {
          // 创建列表项
          ListItem() {
            // 创建列布局
            Column() {
              // 调用分类头部构建器
              this.CategoryHeaderBuilder(repeatItem.item.name)
              // 创建体验卡片
              ExperienceCard({
                discoverContents: repeatItem.item.contents,
                handleItemClick: (content: DiscoverContent) => {
                  // 跳转文章详情
                  this.jumpArticleDetail(content);
                },
              })
                // 设置外边距
                .margin({
                  left: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM ?
                  $r('sys.float.padding_level8') : 0,
                  right: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.SM ?
                  $r('sys.float.padding_level8') : 0,
                })
            }
          }
        })
        // 设置开发者模板
        .template(ArticleTypeEnum.DEVELOPER.toString(), (repeatItem: RepeatItem<DiscoverCardData>) => {
          // 创建列表项
          ListItem() {
            // 创建列布局
            Column() {
              // 调用分类头部构建器
              this.CategoryHeaderBuilder(repeatItem.item.name)
              // 创建开发者卡片
              DeveloperCard({
                discoverContents: repeatItem.item.contents,
                handleItemClick: (content: DiscoverContent) => {
                  // 跳转文章详情
                  this.jumpArticleDetail(content);
                },
              })
            }
          }
        })
      // 创建列表项
      ListItem() {
        // 创建加载更多项目构建器
        LoadingMoreItemBuilder(this.explorationState.loadingModel)
      }
      // 设置内边距
      .padding({
        left: this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.LG ?
          CommonConstants.TAB_BAR_WIDTH + CommonConstants.SPACE_16 : CommonConstants.SPACE_16,
        right: CommonConstants.SPACE_16,
        bottom: (this.globalInfoModel.naviIndicatorHeight +
          (new BreakpointType({
            sm: CommonConstants.TAB_BAR_HEIGHT,
            md: CommonConstants.TAB_BAR_HEIGHT,
            lg: 0,
          }).getValue(this.globalInfoModel.currentBreakpoint))),
      })
    }
    // 设置宽度
    .width('100%')
    // 设置高度
    .height('100%')
    // 设置裁剪
    .clip(false)
    // 设置滚动条
    .scrollBar(BarState.Off)
    // 设置边缘效果
    .edgeEffect(this.explorationState.hasEdgeEffect ? EdgeEffect.Spring : EdgeEffect.None)
    // 设置滚动帧开始事件
    .onScrollFrameBegin((offset: number, state: ScrollState) => {
      // 定义参数
      const param: CalculateHeightParam = { offset, state, yOffset: this.listScroller.currentOffset().yOffset };
      // 发送计算横幅高度事件
      const bannerChangeHeight: boolean | void = this.viewModel.sendEvent<CalculateHeightParam>({
        type: BaseHomeEventType.CALCULATE_BANNER_HEIGHT,
        param,
      });
      // 如果横幅高度改变
      if (bannerChangeHeight) {
        // 返回偏移量为0
        return { offsetRemain: 0 };
      }
      // 返回原偏移量
      return { offsetRemain: offset };
    })
    // 设置滚动完成事件
    .onDidScroll(() => {
      // 发送处理滚动偏移事件
      this.viewModel.sendEvent<OffsetParam>({
        type: BaseHomeEventType.HANDLE_SCROLL_OFFSET,
        param: { yOffset: this.listScroller.currentOffset().yOffset, tabIndex: TabBarType.COMMUNITY },
      });
    })
    // 设置背景颜色
    .backgroundColor($r('sys.color.background_secondary'))
  }

  // 使用Builder装饰器定义顶部标题视图构建器
  @Builder
  TopTitleViewBuilder() {
    // 创建全屏导航
    FullScreenNavigation({
      topNavigationData: this.explorationState.topNavigationData,
    })
  }

  // 定义构建方法
  build() {
    // 创建导航组件
    Navigation(this.explorationPageContext.navPathStack) {
      // 创建基础首页视图
      BaseHomeView({
        loadingModel: this.explorationState.loadingModel,
        contentView: () => {
          // 调用内容视图构建器
          this.ContentViewBuilder()
        },
        topTitleView: () => {
          // 调用顶部标题视图构建器
          this.TopTitleViewBuilder()
        },
        reloadData: () => {
          // 发送加载发现页面事件
          this.viewModel.sendEvent({ type: ExplorationEventType.LOAD_DISCOVERY_PAGE, param: null });
        },
      })
    }
    // 设置导航模式
    .mode(NavigationMode.Stack)
    // 隐藏标题栏
    .hideTitleBar(true)
  }
}