// 导入通用模块中的基础视图模型、断点类型枚举、全局信息模型、加载状态和页面上下文
import { BaseVM, BreakpointTypeEnum, GlobalInfoModel, LoadingStatus, PageContext } from '@ohos/common';
// 导入通用业务模块中的组件详情参数、示例详情参数和标签栏类型
import { ComponentDetailParams, SampleDetailParams, TabBarType } from '@ohos/commonbusiness';
// 导入发现内容类型
import type { DiscoverContent } from '../model/DiscoverData';
// 导入探索详情状态
import { ExplorationDetailState } from './ExplorationDetailState';

// 导出文章详情视图模型类，继承基础视图模型
export class ArticleDetailViewModel extends BaseVM<ExplorationDetailState> {
  // 定义公共构造函数
  public constructor() {
    // 调用父类构造函数
    super(new ExplorationDetailState());
    // 设置顶部导航数据的模糊效果为true
    this.state.topNavigationData.isBlur = true;
  }

  // 定义发送事件的方法
  sendEvent<T>(eventParam: ExplorationDetailEventParam<T>): Promise<void> | void {
    // 如果事件类型为获取文章详情
    if (eventParam.type === ExplorationDetailEventType.GET_ARTICLE_DETAIL) {
      // 返回获取文章详情方法
      return this.getArticleDetail(eventParam.param as DetailParam);
    // 如果事件类型为弹出
    } else if (eventParam.type === ExplorationDetailEventType.POP) {
      // 获取参数
      const param = eventParam.param as PopParam;
      // 返回弹出方法
      return this.pop(param.animation, param.tabBarView);
    // 如果事件类型为跳转原生页面
    } else if (eventParam.type === ExplorationDetailEventType.JUMP_NATIVE_PAGE) {
      // 返回跳转原生页面方法
      return this.jumpNativePage(eventParam.param as NativePageParam);
    }
    // 抛出未实现方法错误
    throw new Error('Method not implemented.');
  }

  // 定义私有获取文章详情的方法
  private getArticleDetail(detailParam: DetailParam): Promise<void> {
    // 设置加载状态为加载中
    this.state.loadingModel.loadingStatus = LoadingStatus.LOADING;
    // 设置内容
    this.state.content = detailParam.content;
    // 设置内容详情URL
    this.state.content.detailsUrl = `resource://resfile/${detailParam.content.detailsUrl}`;
    // 设置顶部导航数据的返回点击事件
    this.state.topNavigationData.onBackClick = detailParam.onBackClick;
    // 返回解析的Promise
    return Promise.resolve();
  }

  // 定义私有弹出的方法
  private pop(animated: boolean = true, tabBarView: number = TabBarType.HOME): void {
    // 获取全局信息模型
    const globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
    // 定义当前路径栈
    let currentPathStack: PageContext = AppStorage.get('pageContext') as PageContext;
    // 如果当前断点为XL
    if (globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL) {
      // 如果标签栏视图为首页
      if (tabBarView === TabBarType.HOME) {
        // 获取组件列表页面上下文
        currentPathStack = AppStorage.get('componentListPageContext')!;
      // 如果标签栏视图为示例
      } else if (tabBarView === TabBarType.SECURITY) {
        // 获取示例页面上下文
        currentPathStack = AppStorage.get('samplePageContext')!;
      } else {
        // 获取探索页面上下文
        currentPathStack = AppStorage.get('explorationPageContext')!;
      }
    }
    // 弹出页面
    currentPathStack.popPage(animated);
  }


  // 定义私有跳转原生页面的方法
  private jumpNativePage(param: NativePageParam): void {
    // 获取全局信息模型
    const globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
    // 定义页面上下文
    let pageContext: PageContext = AppStorage.get('pageContext') as PageContext;
    // 如果当前断点为XL
    if (globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL) {
      // 如果标签栏视图为首页
      if (param.tabBarView === TabBarType.HOME) {
        // 获取组件列表页面上下文
        pageContext = AppStorage.get('componentListPageContext') as PageContext;
      // 如果标签栏视图为示例
      } else if (param.tabBarView === TabBarType.SECURITY) {
        // 获取示例页面上下文
        pageContext = AppStorage.get('samplePageContext') as PageContext;
      } else {
        // 获取探索页面上下文
        pageContext = AppStorage.get('explorationPageContext') as PageContext;
      }
    }
    // 打开页面
    pageContext.openPage({
      routerName: param.type === 'component' ? 'ComponentDetailView' : 'SampleDetailView',
      param: param.type === 'component' ?
        {
          componentName: param.componentName,
          componentId: param.id,
        } as ComponentDetailParams :
        {
          currentIndex: param.currentIndex,
          sampleCardId: param.id,
        } as SampleDetailParams,
    }, true);
  }
}

// 导出探索详情事件类型枚举
export enum ExplorationDetailEventType {
  // 获取文章详情
  GET_ARTICLE_DETAIL = 'getArticleDetail',
  // 弹出
  POP = 'pop',
  // 处理标题效果
  HANDLE_TITLE_EFFECT = 'handleTitleEffect',
  // 跳转原生页面
  JUMP_NATIVE_PAGE = 'jumpNativePage',
}

// 导出探索详情事件参数接口
export interface ExplorationDetailEventParam<T> {
  // 事件类型
  type: ExplorationDetailEventType;
  // 参数
  param: T;
}

// 导出详情参数接口
export interface DetailParam {
  // 内容
  content: DiscoverContent;
  // 返回点击事件
  onBackClick: Function;
}

// 导出弹出参数接口
export interface PopParam {
  // 动画
  animation: boolean;
  // 标签栏视图
  tabBarView: number;
}

// 导出原生页面参数接口
export interface NativePageParam {
  // 标签栏视图
  tabBarView: number;
  // 类型
  type: string;
  // ID
  id: number;
  // 当前索引（可选）
  currentIndex?: number;
  // 组件名称（可选）
  componentName?: string;
}